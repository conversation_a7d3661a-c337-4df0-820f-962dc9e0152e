<!DOCTYPE html>
<!-- mocked drawing page -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Draw</title>

    <!-- this is important -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

</head>
<body>

<!-- embed start -->
<script src="../../dist/embed.js"></script>
<script type="text/javascript">

    /*
    Using Klecks in a drawing community:
    - on first time opening, start with a manually created project (klecks.openProject)
    - on submit, upload psd (and png) to the server
    - on continuing a drawing, read psd that was stored on server (klecks.readPsd -> klecks.openProject)
     */

    const psdURL = 'example-drawing.psd';

    let saveData = (function () {
        let a = document.createElement("a");
        document.body.appendChild(a);
        a.style = "display: none";
        return function (blob, fileName) {
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = fileName;
            a.click();
            window.URL.revokeObjectURL(url);
        };
    }());

    const klecks = new Klecks({
        // disableAutoFit: true,
        onSubmit: async (onSuccess, onError) => {
            // download png
            saveData(await klecks.getPNG(), 'drawing.png');

            /*// download psd
            klecks.getPSD().then((blob) => {
                saveData(blob, 'drawing.psd');
            });*/

            setTimeout(() => {
                onSuccess();
                location.reload();
            }, 500);
        },
    });

    if (psdURL) {
        fetch(new Request(psdURL)).then(response => {
            return response.arrayBuffer();
        }).then(buffer => {
            return klecks.readPSD(buffer); // resolves to Klecks project
        }).then(project => {
            klecks.openProject(project);
        }).catch(e => {
            klecks.initError('failed to read image');
        });

    } else {
        const useFill = true;
        // fill is more efficient if layer is one color
        const layerImage = useFill ? {fill: '#fff'} : (() => {
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            ctx.save();
            ctx.fillStyle = '#fff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#000';
            for(let i = 0; i < 10; i++) {
                ctx.fillRect(Math.random() * canvas.width, Math.random() * canvas.height, 10, 10);
            }
            ctx.restore();
            return canvas;
        })();

        klecks.openProject({
            width: 100,
            height: 100,
            layers: [{
                name: 'Background',
                isVisible: true,
                opacity: 1,
                mixModeStr: 'source-over',
                image: layerImage,
            }]
        });
    }
</script>
<!-- embed end -->

</body>
</html>
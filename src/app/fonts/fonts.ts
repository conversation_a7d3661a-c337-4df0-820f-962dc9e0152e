import fontCaveat from 'url:./Caveat-Regular.woff2';
import fontClimateCrisis from 'url:./ClimateCrisis-Regular-VariableFont_YEAR.woff2';
import fontCorrectionBrush from 'url:./CorrectionBrush.woff2';
import fontDancingScript from 'url:./DancingScript-Regular.woff2';
import fontDiabolik from 'url:./Diabolik-Regular-VF.woff2';
import fontFreckleFace from 'url:./FreckleFace-Regular.woff2';
import fontGloock from 'url:./Gloock-Regular.woff2';
import fontPacha from 'url:./Pacha.woff2';
import fontGochiHand from 'url:./GochiHand-Regular.woff2';
import fontPassionOne from 'url:./PassionOne-Bold.woff2';
import fontPixeloidSans from 'url:./PixeloidSans.woff2';
import fontPlaypenSans from 'url:./PlaypenSans-Thin.woff2';
import fontQuicksandLight from 'url:./Quicksand-Light.woff2';
import fontSilkscreen from 'url:./Silkscreen-Regular.woff2';
import fontTehroc from 'url:./Tehroc-Regular.woff2';
import fontYunga from 'url:./YUNGA-Display.woff2';

export const FONTS: {
    name: string;
    url: string;
}[] = [
    { name: 'Caveat', url: fontCaveat },
    { name: 'ClimateCrisis', url: fontClimateCrisis },
    { name: 'CorrectionBrush', url: fontCorrectionBrush },
    { name: 'DancingScript', url: fontDancingScript },
    { name: 'Diabolik', url: fontDiabolik },
    { name: 'FreckleFace', url: fontFreckleFace },
    { name: 'Gloock', url: fontGloock },
    { name: 'GochiHand', url: fontGochiHand },
    { name: 'Pacha', url: fontPacha },
    { name: 'PassionOne', url: fontPassionOne },
    { name: 'PixeloidSans', url: fontPixeloidSans },
    { name: 'PlaypenSans', url: fontPlaypenSans },
    { name: 'Quicksand', url: fontQuicksandLight },
    { name: 'Silkscreen', url: fontSilkscreen },
    { name: 'Tehroc', url: fontTehroc },
    { name: 'YUNGA', url: fontYunga },
];

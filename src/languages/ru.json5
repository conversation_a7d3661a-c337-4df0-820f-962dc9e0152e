{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: 'Меню влево/вправо'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: 'Показать/скрыть инструменты'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: 'Прокрутка'
  },
  donate: {
    original: 'Donate',
    value: 'Пожертвовать'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'Главная страница'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: 'Открыть в новой вкладке'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: 'Изменить'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'Файл'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'Кисть'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'Заливка'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'Градиент'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: 'Форма'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'Текст'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: 'Инструмент «Рука»'
  },
  'tool-select': {
    original: 'Select Tool',
    value: 'Инструмент «Выделение»'
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'Приблизить/отдалить'
  },
  'tool-more-tools': {
    hint: 'when hovering the caret on the tool button',
    original: 'More Tools',
    value: 'Другие инструменты'
  },
  undo: {
    original: 'Undo',
    value: 'Отмена'
  },
  redo: {
    original: 'Redo',
    value: 'Повторить действие'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'Ручка'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: 'Переход'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'Эскиз'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'Пиксели'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'Графика особой формы'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: 'Палец'
  },
  'brush-size': {
    original: 'Size',
    value: 'Размер'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: 'Наложение'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: 'Переключение чувствительности давления'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: 'Круг'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'Мел'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'Каллиграфия'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: 'Квадрат'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'Масштабирование'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'Дизеринг'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: 'Заливка'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: 'Обводка'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: 'Горизонтальная симметрия'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: 'Вертикальная симметрия'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'Градиент'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: 'Прозрачный фон'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: 'Стабилизатор'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: 'Стабилизатор обводки'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'Пипетка'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'Вторичный цвет'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: 'Ввод цвета вручную'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'Шестнадцатеричный'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Скопировать'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'OK'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'Отмена'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: 'Закрыть'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'Активный слой'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'Слой'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'копия'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: 'Наложение'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: 'Новый слой'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: 'Удалить слой'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: 'Копировать слой'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: 'Объединить со слоём ниже'
  },
  'layers-clear': {
    hint: 'to make a layer empty',
    original: 'Clear layer',
    value: 'Очистить слой'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: 'Объединить все'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'Переименовать'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'Активный слой видимый'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'Активный слой скрыт'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'Видимость слоя'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: 'обычный'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: 'затемнение'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: 'умножение'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: 'затемнение основы'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: 'осветление'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'экранирование'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: 'осветление основы'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'перекрытие'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'мягкий свет'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'жёсткий свет'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: 'разница'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: 'исключение'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: 'цветовой тон'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: 'насыщенность'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'цветность'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: 'светимость'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'Переименовать слой'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'Название'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'Сбросить название'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'Эскиз'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: 'Цвета'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: 'Затенение'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: 'Линии'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: 'Эффекты'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: 'Передний план'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'Объединение/смешивание слоёв'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: 'Объединяет выделенный слой с тем, который находится под ним. Выберите режим смешивания:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'Нет автосохранения, нет облачного хранилища'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: 'Новый'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: 'Импорт'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: 'Сохранить'
  },
  'file-format': {
    original: 'File Format',
    value: 'Формат файла'
  },
  'file-show-save-dialog': {
    hint: 'setting for showing/not showing a dialog where user picks folder and filename',
    original: 'Show save dialog',
    value: ''
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Скопировать'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'Скопировать в буфер обмена'
  },
  'file-paste': {
    original: 'Paste',
    value: ''
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'Поделиться'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'Хранилище браузера'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: 'Восстанавливается при повторном открытии страницы'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'О хранилище браузера'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'Не удаётся получить доступ'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: 'Пусто'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: 'Сохранить'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: 'Очистить'
  },
  'file-storage-clear-prompt': {
    original: 'Clear Browser Storage?',
    value: 'Очистить хранилище браузера?'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: 'Сохранение'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: 'Перезаписать'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x} мин. назад'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x} ч. назад'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x} дн. назад'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: '> 1 месяц назад'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'Восстановлено из хранилища браузера'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'Сохранено в хранилище браузера'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'Не удалось сохранить в хранилище браузера'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: 'Не удалось сохранить. Возможные причины:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'Не хватает дискового пространства'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'Хранилище отключено во вкладке инкогнито'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'Браузер не поддерживает хранилище'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: 'Не удалось очистить.'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'Загрузить'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'Слой очищен'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: 'Очищена выделенная область'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Залито'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: 'Залито выделенное'
  },
  'new-title': {
    original: 'New Image',
    value: 'Новое изображение'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Текущее'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Подгонка'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Огромная'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Квадрат'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Альбомная'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Книжная'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Экран'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Видео'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'Формат бумаги'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'пикс.'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: 'Соотношение'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Загрузить на Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'Любой, у кого есть ссылка на загруженное вами изображение, сможет его просмотреть.'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: 'Название'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: 'Безымянный'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'Подпись'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'Загрузить'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'Загрузка...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'Загрузка удалась'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'Загрузка не удалась.'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'Чтобы удалить своё изображение с Imgur, зайдите на:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'Скопировать в буфер обмена'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: 'Рамка'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: 'Нажмите правую кнопку мыши или нажмите и удерживайте, чтобы скопировать.'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'В буфер обмена'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'Скопировано.'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: 'Применение обрезки'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: 'Перетащите для кадрирования'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: 'Кадрир./растягив.'
  },
  'filter-flip': {
    original: 'Flip',
    value: 'Перевернуть'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'Перспектива'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'Изм. размера'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: 'Поворот'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: 'Трансформ.'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: 'Ярк./Контраст.'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'Кривые'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: 'Цв. тон/Насыщ.'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'Инверсия'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: 'Глуб. резкости'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: 'Прозрачность'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'Треуг. размытие'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'Контурная резкость'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: 'Кадрирование / растягивание'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: 'Обрезка или растягивание изображения.'
  },
  'filter-crop-left': {
    original: 'Left',
    value: 'Влево'
  },
  'filter-crop-right': {
    original: 'Right',
    value: 'Вправо'
  },
  'filter-crop-top': {
    original: 'Top',
    value: 'Вверх'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: 'Вниз'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: 'Правило третей'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: 'Заливка'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: 'Перевернуть'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'Переворачивание слоя или всего изображения.'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: 'По горизонтали'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: 'По вертикали'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: 'Перевернуть изображение'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: 'Перевернуть слой'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'Перспектива'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: 'Трансформирование выделенного слоя.'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'Изменение размера'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: 'Изменение размера изображения.'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: 'Поворот'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: 'Поворачивание изображения.'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'Слой пустой.'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: 'Трансформация'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: 'Трансформация выделенного слоя. Удерживайте Shift для дополнительного поведения.'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: 'Поворот'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: 'Перевернуть'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: 'Центрировать'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: 'Сохранить'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: 'Привязать'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: 'Привязать вращение и положение'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: 'Яркость / Контрастность'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: 'Изменение яркости и контрастности для выделенного слоя.'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: 'Яркость'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'Контраст'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'Кривые'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: 'Применение кривых к выделенному слою.'
  },
  'filter-curves-all': {
    original: 'All',
    value: 'Все'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: 'Цветовой тон / Насыщенность'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: 'Изменение оттенка и насыщенности для выделенного слоя.'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: 'Цветовой тон'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: 'Насыщенность'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: ': применено'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: 'Глубина резкости'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: 'Применение глубины резкости к выделенному слою.'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'Радиус размытия'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'Радиус градиента'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: 'Прозрачность'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: 'Создание альфа-канала для выделенного слоя:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: 'Инвертированная яркость'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: 'Яркость'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Замена RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'Треугольное размытие'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: 'Применение треугольного размытия к выделенному слою.'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'Контурная резкость'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: 'Повышение резкости выделенного слоя, масштабируя пиксели от среднего значения их соседей.'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: 'Интенсивность'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'Сетка'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: 'Отображение сетки на выделенном слое.'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'Шум'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: 'Добавление шума к выделенному слою.'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'Масштаб'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Прозрачность'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'Шаблон'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: 'Создание шаблона на выделенном слое. Перетащите предпросмотр для дополнительных элементов управления.'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'Искажение'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: 'Искажение выделенного слоя.'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'Фаза'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'Размер шага'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: 'Синхронизация XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: 'Испр. перспективы'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: 'Исправление перспективы'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: 'Добавление исправления перспективы к выделенному слою. Перетащите предпросмотр, чтобы переместить.'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: 'Линии'
  },
  'dropper-drop': {
    hint: 'as in, drag and drop a file',
    original: 'Drop to import',
    value: 'Отпустите для добавления'
  },
  'dropper-as-image': {
    original: 'As New Image',
    value: 'Нового изображения'
  },
  'dropper-as-layer': {
    original: 'As Layer',
    value: 'Слоя'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: 'Открытие файла...'
  },
  'import-title': {
    original: 'Import Image',
    value: 'Импорт изображения'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: 'Изображение слишком большое, оно будет уменьшено.'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'Как слой'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: 'Как изображение'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: 'Импорт изображения как нового слоя'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: 'Регулировка положения импортированного изображения.'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: 'Достигнуто ограничение слоёв. Изображение будет помещено на существующий слой.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'Подгонка'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'Выполнить сведение'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'Неподдерживаемый тип файла. Поддерживаемые типы см. во вкладке «Справка».'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: 'Не удалось загрузить изображение. Возможно, файл повреждён.'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'Неподдерживаемые функции. PSD необходимо было сведить.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'Поддержка PSD ограничена. Сведённый вариант, скорее всего, будет выглядеть правильно.'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: 'Изображение превышает максимальные размеры {x} x {x} пикселей. Невозможно импортировать.'
  },
  'import-psd-size': {
    original: 'Image size',
    value: 'Размер изображения'
  },
  'clipboard-read-fail': {
    original: 'Failed to read from clipboard.',
    value: ''
  },
  'clipboard-no-image': {
    original: 'No image found in clipboard.',
    value: ''
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'Сброс'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'Подгонка'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: 'Перемещение по холсту с инерцией'
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: 'Допуск'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: 'Образец'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'Из каких слоёв брать образцы цвета'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: 'Всех'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: 'Активный'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: 'Выше'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: 'Расш. заполн.'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: 'Расширить залитую область (в пикселях)'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: 'Смеж. пикс'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: 'Залить только соединённые области'
  },
  'gradient-linear': {
    original: 'Linear',
    value: 'Линейный'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: 'Линейно-зеркальный'
  },
  'gradient-radial': {
    original: 'Radial',
    value: 'Лучевой'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'Обводка'
  },
  'shape-fill': {
    original: 'Fill',
    value: 'Заливка'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: 'Прямоугольник'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: 'Эллипс'
  },
  'shape-line': {
    original: 'Line',
    value: 'Линия'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: 'Ширина линии'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: 'Внешне'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: 'Фиксированная 1:1'
  },
  'shape-auto-pan': {
    original: 'Auto-pan',
    value: ''
  },
  'shape-auto-pan-title': {
    original: 'Automatically moves as you draw',
    value: ''
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'Нажмите на холст, чтобы разместить текст'
  },
  'text-title': {
    original: 'Add Text',
    value: 'Добавить текст'
  },
  'text-text': {
    original: 'Text',
    value: 'Текст'
  },
  'text-font': {
    original: 'Font',
    value: 'Шрифт'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'Ваш текст'
  },
  'text-color': {
    original: 'Color',
    value: 'Цвет'
  },
  'text-size': {
    original: 'Size',
    value: 'Размер'
  },
  'text-line-height': {
    original: 'Line Height',
    value: 'Высота линии'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: 'Расстояние между буквами'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: 'Слева'
  },
  'text-center': {
    original: 'Center',
    value: 'По центру'
  },
  'text-right': {
    original: 'Right',
    value: 'Справа'
  },
  'text-italic': {
    original: 'Italic',
    value: 'Курсив'
  },
  'text-bold': {
    original: 'Bold',
    value: 'Жирный'
  },
  'select-select': {
    original: 'Select',
    value: 'Выделение'
  },
  'select-transform': {
    original: 'Transform',
    value: 'Трансформация'
  },
  'select-lasso': {
    original: 'Lasso',
    value: 'Лассо'
  },
  'select-polygon': {
    original: 'Polygon',
    value: 'Многоугольник'
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: 'Замена'
  },
  'select-boolean-add': {
    original: 'Add',
    value: 'Добавление'
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: 'Вычитание'
  },
  'select-all': {
    original: 'All',
    value: 'Всё'
  },
  'select-invert': {
    original: 'Invert',
    value: 'Инвертировать'
  },
  'select-reset': {
    original: 'Reset',
    value: 'Сброс'
  },
  'select-fill': {
    original: 'Fill',
    value: 'Залить'
  },
  'select-erase': {
    original: 'Erase',
    value: 'Стереть'
  },
  'select-transform-clone': {
    original: 'Clone',
    value: 'Клонировать'
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: 'Клонировано'
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: 'Переместить в слой:'
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: 'Трансформация применена'
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: 'Выделенная область на активном слое пуста.'
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: 'Несохранённая работа'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: 'Изображение не было сохранено в течение {a} мин{b}. Сохраните сейчас, чтобы предотвратить возможную потерю.'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'Сохранить как PSD'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD запомнит все слои.'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: 'Вы можете создать резервную копию своего рисунка.'
  },
  submit: {
    original: 'Submit',
    value: 'Отправить'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: 'Отправка рисунка'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: 'Отправить рисунок?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: 'Отправка'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Загрузка приложения'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: 'Ожидание изображения'
  },
  unsaved: {
    original: 'Unsaved',
    value: 'Не сохранено'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'Справка'
  },
  'tab-settings': {
    original: 'Settings',
    value: 'Настройки'
  },
  'settings-language': {
    original: 'Language',
    value: 'Язык'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'Обновится после перезагрузки.'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'Тема'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: 'Напоминание сохр.'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: 'отключено'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: 'Отключить напоминание о сохранении?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: 'Здесь нет автосохранения, а вкладки браузера не хранятся вечно. Если вы не будете периодически сохранять, то, скорее всего, потеряете рисунок.'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: 'Отключить на свой страх и риск?'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: 'Отключить'
  },
  'theme-dark': {
    original: 'Dark',
    value: 'Тёмная'
  },
  'theme-light': {
    original: 'Light',
    value: 'Светлая'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Условия обслуживания'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Лицензии'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Исходный код'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: 'авто'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: 'Приближение'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: 'Отдаление'
  },
  radius: {
    original: 'Radius',
    value: 'Радиус'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: 'Сохранение пропорций'
  },
  width: {
    original: 'Width',
    value: 'Ширина'
  },
  height: {
    original: 'Height',
    value: 'Высота'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: 'Прозрач.'
  },
  scatter: {
    hint: 'for brush scatter',
    original: 'Scatter',
    value: ''
  },
  red: {
    original: 'Red',
    value: 'Красный'
  },
  green: {
    original: 'Green',
    value: 'Зелёный'
  },
  blue: {
    original: 'Blue',
    value: 'Синий'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: 'Ластик'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: 'По центру'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'Слои'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: 'Фон'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'Алгоритм масштабирования'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'Гладкий'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'Пиксельный'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'Предпросмотр'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'Привязка'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: 'Угловая привязка 45°'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: 'Альфа-блокировка'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: 'Блокирует альфа-канал слоя'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: 'Реверсивный'
  },
  'compare-before': {
    original: 'Before',
    value: 'До'
  },
  'compare-after': {
    original: 'After',
    value: 'После'
  },
  loading: {
    original: 'Loading',
    value: 'Загрузка'
  },
  more: {
    original: 'More',
    value: 'Ещё'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x} мин.'
  },
  wip: {
    original: 'Work in progress',
    value: 'В разработке'
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: 'Двойное нажатие или уменьшение масштаба позволяет сбросить масштаб браузера.'
  },
  dismiss: {
    original: 'Dismiss',
    value: 'Убрать'
  }
}
{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: '左右切り替え'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: '表示/非表示'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: 'スクロール'
  },
  donate: {
    original: 'Donate',
    value: '寄付'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'ホーム'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: '新しいタブで開く'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: '編集'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'ファイル'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'ブラシ'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'バケツ'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'グラデーション'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: '図形'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'テキスト'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: '手のひらツール'
  },
  'tool-select': {
    original: 'Select Tool',
    value: ''
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'ズーム'
  },
  'tool-more-tools': {
    hint: 'when hovering the caret on the tool button',
    original: 'More Tools',
    value: ''
  },
  undo: {
    original: 'Undo',
    value: 'アンドゥ'
  },
  redo: {
    original: 'Redo',
    value: 'リドゥ'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'ペン'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: '水彩'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'スケッチ'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'ピクセル'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'ケミー'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: '指先ツール'
  },
  'brush-size': {
    original: 'Size',
    value: 'サイズ'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: '水分量'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: '筆圧'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: '円'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'チョーク'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'カリグラフィー'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: '角'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'スケール'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'ドット'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: '塗り潰し'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: '線'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: '左右対称'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: '上下対称'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'グラデーション'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: '背景の透明化'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: '手ぶれ補正'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: '手ぶれ補正'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'スポイト'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'セカンダリーカラー'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: '数値入力'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'Hex'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'コピー'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Ok'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'キャンセル'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: '閉じる'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'アクティブレイヤー'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'レイヤー'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'コピー'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: '合成方法'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: '新規'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: '削除'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: '複製'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: '下のレイヤーと結合'
  },
  'layers-clear': {
    hint: 'to make a layer empty',
    original: 'Clear layer',
    value: ''
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: ''
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'リネーム'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'レイヤー表示中'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'レイヤー非表示中'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'レイヤー表示/非表示'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: '通常'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: '比較(暗)'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: '乗算'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: '焼き込み'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: '比較(明)'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'スクリーン'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: '覆い焼き'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'オーバーレイ'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'ソフトライト'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'ハードライト'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: '差の絶対値'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: '除外'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: '色相'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: '彩度'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'カラー'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: '輝度'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'リネーム'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'レイヤー名'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'クリア'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'ラフ'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: '色'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: '影'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: '線'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: '効果'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: '上'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'レイヤー結合'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: '下のレイヤーと結合します。 合成方法を選択してください。:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'クラウドに自動保存できません'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: '新規'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: '配置'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: '保存'
  },
  'file-format': {
    original: 'File Format',
    value: '保存形式'
  },
  'file-show-save-dialog': {
    hint: 'setting for showing/not showing a dialog where user picks folder and filename',
    original: 'Show save dialog',
    value: ''
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'コピー'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'クリップボードにコピー'
  },
  'file-paste': {
    original: 'Paste',
    value: ''
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'シェア'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'ブラウザストレージ'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: '再読み込み時に復元します。'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'ブラウザストレージについて'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'アクセスできません。'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: '空'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: '保管'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: '削除'
  },
  'file-storage-clear-prompt': {
    original: 'Clear Browser Storage?',
    value: ''
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: '保管中。'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: '上書き'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x}分前'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x}時間前'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x}日前'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: '一ヶ月以上'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'ブラウザストレージから復元しました。'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'ブラウザストレージに保存します。'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'ブラウザストレージへの保存に失敗しました。'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: '保存に失敗した原因:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'ディスク容量が不足しています。'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'シークレットタブのためストレージが使えません。'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'このブラウザはストレージをサポートしていません。'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: '削除に失敗しました。'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'アップロード'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'レイヤーをクリアしました。'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: ''
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Filled'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: ''
  },
  'new-title': {
    original: 'New Image',
    value: '新規画像'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Current'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Fit'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Oversize'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Square'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Landscape'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Portrait'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Screen'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Video'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'DIN Paper'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: '縦横比'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Imgurに投稿'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'アップロードされた画像は、URLを知っている人なら誰でも見ることができます。'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: '題名'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: '名称未設定'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'キャプション'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'アップロード'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'アップロードしています。'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'アップロードに成功しました。'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'アップロードに失敗しました。'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'ここから削除できます:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'クリップボードにコピー'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: '切り取り'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: '右クリックまたは長押しでコピー。'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'コピー'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'コピーしました。'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: '切り取ります'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: '切り取る範囲'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: '切り取り'
  },
  'filter-flip': {
    original: 'Flip',
    value: '反転'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'パース'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'リサイズ'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: '回転'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: '自由変形'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: '明度'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'カーブ'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: '色相'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'ネガポジ'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: '被写界深度'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: '透明化'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'ブラー'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'シャープ'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: '切り取り'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: '画像のトリミングまたは拡張。'
  },
  'filter-crop-left': {
    original: 'Left',
    value: '左'
  },
  'filter-crop-right': {
    original: 'Right',
    value: '右'
  },
  'filter-crop-top': {
    original: 'Top',
    value: '上'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: '下'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: '三分割法'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: '塗り潰し'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: '反転'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'レイヤーまたは画像全体を反転します。'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: '左右反転'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: '上下反転'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: '画像'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: 'レイヤー'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'パース'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: '選択中のレイヤーを変形します。'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'リサイズ'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: '画像のサイズを変更します。'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: '回転'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: '画像を回転。'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'レイヤーが空です。'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: '自由変形'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: '選択中のレイヤーを変形します。シフトキーを押下すると動作が追加されます。'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: '角度'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: '反転'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: '中央'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: '縦横比'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: 'スナップ'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: '回転･移動時にスナップします'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: '明度/コントラスト'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: '選択中のレイヤーの明度/コントラストを変更します。'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: '明度'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'コントラスト'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'カーブ'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: '選択中のレイヤーの色調をカーブで調整します。'
  },
  'filter-curves-all': {
    original: 'All',
    value: '全て'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: '色相/彩度'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: '選択中のレイヤーの色相と彩度を変更します。'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: '色相'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: '彩度'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: '反転しました。'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: '被写界深度'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: '選択中のレイヤーのピントの合う範囲を調整します。'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'ぼかし半径'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'グラデーション半径'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: '透明化'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: '選択中のレイヤーの輝度を透明度に変換します。'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: '輝度反転'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: '輝度'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Replace RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'ブラー'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: '選択中のレイヤーにぼかしをかけます。'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'アンシャープマスク'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: '選択中のレイヤーの画像をシャープにします。'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: '強さ'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'グリッド'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: '選択レイヤーにグリッドを追加します。'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'ノイズ'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: '選択レイヤーにノイズを追加します。'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'スケール'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: '透明'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'パターン'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: '選択レイヤーにパターンを作ります。プレビューはドラッグできます。'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'ゆがみ'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: '選択レイヤーにゆがみを適用します。'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'フェイズ'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'ステップサイズ'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: '同期 XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: '集中線'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: '集中線'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: '選択レイヤーに集中線を追加します。プレビューはドラッグできます。'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: '線の数'
  },
  'dropper-drop': {
    hint: 'as in, drag and drop a file',
    original: 'Drop to import',
    value: ''
  },
  'dropper-as-image': {
    original: 'As New Image',
    value: ''
  },
  'dropper-as-layer': {
    original: 'As Layer',
    value: ''
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: '画像を開いています。'
  },
  'import-title': {
    original: 'Import Image',
    value: '画像の読み込み'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: '画像が大きすぎるため、縮小します。'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'レイヤーとして'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: '画像として'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: '新規レイヤーに読み込みます。'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: '画像の位置を調整してください。'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: '制限枚数を超えました。画像は既存のレイヤーに配置されます。'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'フィット'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'レイヤー結合'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'サポートされていないファイル形式です。使用可能なファイル形式はヘルプを参照してください。'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: '画像の読み込みに失敗しました。ファイルが破損している可能性があります。'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'サポートされていない形式です。'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'PSDのサポートは限定的です。結合すれば正しく表示されるかもしれません。'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: '画像が {x} x {x} ピクセルを超えているためインポートできません。'
  },
  'import-psd-size': {
    original: 'Image size',
    value: '画像のサイズ'
  },
  'clipboard-read-fail': {
    original: 'Failed to read from clipboard.',
    value: ''
  },
  'clipboard-no-image': {
    original: 'No image found in clipboard.',
    value: ''
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'リセット'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'フィット'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: ''
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: '許容範囲'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: '参照'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'どのレイヤーを参照するか'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: '全て'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: '現在'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: '一つ上'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: '拡張'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: '塗り潰しの範囲を拡張（単位ピクセル）'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: '隣接'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: '隣接した領域のみを塗り潰します'
  },
  'gradient-linear': {
    original: 'Linear',
    value: '線状'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: '反射形'
  },
  'gradient-radial': {
    original: 'Radial',
    value: '放射状'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'ストローク'
  },
  'shape-fill': {
    original: 'Fill',
    value: '塗り潰し'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: '矩形'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: '楕円'
  },
  'shape-line': {
    original: 'Line',
    value: '線'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: '線の幅'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: '外方向'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: '縦横1:1'
  },
  'shape-auto-pan': {
    original: 'Auto-pan',
    value: ''
  },
  'shape-auto-pan-title': {
    original: 'Automatically moves as you draw',
    value: ''
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'クリックしてテキストを入力。'
  },
  'text-title': {
    original: 'Add Text',
    value: 'テキストを追加'
  },
  'text-text': {
    original: 'Text',
    value: ''
  },
  'text-font': {
    original: 'Font',
    value: ''
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'テキストを入力'
  },
  'text-color': {
    original: 'Color',
    value: 'カラー'
  },
  'text-size': {
    original: 'Size',
    value: 'サイズ'
  },
  'text-line-height': {
    original: 'Line Height',
    value: ''
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: ''
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: '左寄せ'
  },
  'text-center': {
    original: 'Center',
    value: '中央'
  },
  'text-right': {
    original: 'Right',
    value: '右寄せ'
  },
  'text-italic': {
    original: 'Italic',
    value: '斜体'
  },
  'text-bold': {
    original: 'Bold',
    value: '太字'
  },
  'select-select': {
    original: 'Select',
    value: ''
  },
  'select-transform': {
    original: 'Transform',
    value: ''
  },
  'select-lasso': {
    original: 'Lasso',
    value: ''
  },
  'select-polygon': {
    original: 'Polygon',
    value: ''
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: ''
  },
  'select-boolean-add': {
    original: 'Add',
    value: ''
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: ''
  },
  'select-all': {
    original: 'All',
    value: ''
  },
  'select-invert': {
    original: 'Invert',
    value: ''
  },
  'select-reset': {
    original: 'Reset',
    value: ''
  },
  'select-fill': {
    original: 'Fill',
    value: ''
  },
  'select-erase': {
    original: 'Erase',
    value: ''
  },
  'select-transform-clone': {
    original: 'Clone',
    value: ''
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: ''
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: ''
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: ''
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: ''
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: '作業を保存していません。'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: '画像は{a}分間{b}保存されませんでした。作業を失わないように今すぐ保存してください。'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'PSDで保存。'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSDは全レイヤーを保持します。'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: '画像をバックアップできます。'
  },
  submit: {
    original: 'Submit',
    value: '送信'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: '画像を送信します。'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: '画像を送信しても宜しいですか?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: '処理しています。'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Loading app'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: '画像を読み込んでいます。'
  },
  unsaved: {
    original: 'Unsaved',
    value: '未保存'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'ヘルプ'
  },
  'tab-settings': {
    original: 'Settings',
    value: '設定'
  },
  'settings-language': {
    original: 'Language',
    value: '言語'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'リロード後に更新されます。'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'テーマ'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: ''
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: ''
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: ''
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: ''
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: ''
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: ''
  },
  'theme-dark': {
    original: 'Dark',
    value: 'ダーク'
  },
  'theme-light': {
    original: 'Light',
    value: 'ライト'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Terms of Service'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Licenses'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Source Code'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: '自動検出'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: '拡大'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: '縮小'
  },
  radius: {
    original: 'Radius',
    value: '半径'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: '縦横比を保持'
  },
  width: {
    original: 'Width',
    value: '幅'
  },
  height: {
    original: 'Height',
    value: '高さ'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: '不透明度'
  },
  scatter: {
    hint: 'for brush scatter',
    original: 'Scatter',
    value: ''
  },
  red: {
    original: 'Red',
    value: '赤'
  },
  green: {
    original: 'Green',
    value: '緑'
  },
  blue: {
    original: 'Blue',
    value: '青'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: '消しゴム'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: '中央'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'レイヤー'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: '背景'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'スケーリング'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'スムーズ'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'ピクセル化'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'プレビュー'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'スナップ'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: '45°の角度にスナップします'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: '透明部分'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: '透明部分の保護'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: '反転'
  },
  'compare-before': {
    original: 'Before',
    value: 'ビフォア'
  },
  'compare-after': {
    original: 'After',
    value: 'アフター'
  },
  loading: {
    original: 'Loading',
    value: ''
  },
  more: {
    original: 'More',
    value: ''
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x}分'
  },
  wip: {
    original: 'Work in progress',
    value: ''
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: ''
  },
  dismiss: {
    original: 'Dismiss',
    value: ''
  }
}
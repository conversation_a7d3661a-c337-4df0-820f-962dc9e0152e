{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: '向左/右切换'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: '显示/隐藏工具'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: '上滑/下滑'
  },
  donate: {
    original: 'Donate',
    value: '赞助'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: '主页'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: '在新页面打开'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: '编辑'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: '文件'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: '画笔工具'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: '油漆桶'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: '渐变'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: '形状'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: '文本'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: '手型工具'
  },
  'tool-select': {
    original: 'Select Tool',
    value: '选择工具'
  },
  'tool-zoom': {
    original: 'Zoom',
    value: '缩放工具'
  },
  'tool-more-tools': {
    hint: 'when hovering the caret on the tool button',
    original: 'More Tools',
    value: ''
  },
  undo: {
    original: 'Undo',
    value: '撤销'
  },
  redo: {
    original: 'Redo',
    value: '重做'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: '画笔'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: '水彩刷'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: '素描笔'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: '像素化'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: '异形图形'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: '晕染'
  },
  'brush-size': {
    original: 'Size',
    value: '画笔大小'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: '晕染'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: '笔压敏度'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: '圆形'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: '粉笔'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: '斜角笔'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: '方形'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: '网格长度'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: '像素抖动'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: '填充'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: '线条'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: '水平对称'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: '垂直对称'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: '渐变'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: '底层透明度'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: '抖动修正'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: '抖动修正线条'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: '颜色选取'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: '二级色'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: '手动输入颜色'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: '16进制'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: '复制'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Ok'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: '取消'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: '关闭'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: '当前图层'
  },
  'layers-layer': {
    original: 'Layer',
    value: '图层'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: '副本'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: '混合模式'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: '新建图层'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: '删除图层'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: '复制图层'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: '向下合并'
  },
  'layers-clear': {
    hint: 'to make a layer empty',
    original: 'Clear layer',
    value: ''
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: '合并全部'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: '重命名'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: '当前图层可见'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: '当前图层隐藏'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: '图层可见性'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: '正常'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: '变暗'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: '正片叠底'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: '颜色加深'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: '变亮'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: '滤色'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: '颜色减淡'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: '叠加'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: '柔光'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: '强光'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: '差值'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: '排除'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: '色相'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: '饱和度'
  },
  'layers-blend-color': {
    original: 'color',
    value: '颜色'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: '明度'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: '重命名图层'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: '重命名'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: '清除'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: '草图'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: '色彩'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: '阴影'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: '线条'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: '视觉效果'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: '前景'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: '向下合并'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: '选中层与底下一层合并。选择混合模式：'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: '不能自动保存，没有云存储'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: '新建'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: '打开'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: '保存'
  },
  'file-format': {
    original: 'File Format',
    value: '文件格式'
  },
  'file-show-save-dialog': {
    hint: 'setting for showing/not showing a dialog where user picks folder and filename',
    original: 'Show save dialog',
    value: ''
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: '复制'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: '复制到剪贴板'
  },
  'file-paste': {
    original: 'Paste',
    value: ''
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: '分享'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: '保存至浏览器'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: '重新打开页面时恢复'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: '关于保存至浏览器'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: '无法获取'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: '无内容'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: '保存'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: '清除'
  },
  'file-storage-clear-prompt': {
    original: 'Clear Browser Storage?',
    value: ''
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: '保存中'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: '覆盖'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x}分钟前'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x}小时前'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x}天前'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: '>一个月前'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: '从浏览器内存中恢复'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: '保存至浏览器内存'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: '保存失败'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: '保存失败。可能是由于：'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: '磁盘空间不足'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: '隐身页面中禁用存储功能'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: '浏览器不支持'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: '清除失败'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: '上传'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: '图层已清除'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: '清除选中区域'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: '图层已填充'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: '填充选中区域'
  },
  'new-title': {
    original: 'New Image',
    value: '新建项目'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: '当前大小'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: '适应页面'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: '大画布'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: '正方形'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: '横向尺寸'
  },
  'new-portrait': {
    original: 'Portrait',
    value: '纵向尺寸'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: '显示分辨率'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: '视频'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: '纸张大小'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: '像素'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: '比例'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: '上传到Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: '他人可以通过生成的图片链接查看你的作品。'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: '标题'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: '未命名'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: '描述'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: '上传'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: '上传中...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: '上传成功'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: '上传失败'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: '要从Imgur删除你的图片, 请访问:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: '复制到剪贴板'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: '裁剪'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: '点击右键或长按进行复制。'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: '至剪贴板'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: '已复制'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: '应用裁剪'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: '拖动裁剪'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: '画布大小'
  },
  'filter-flip': {
    original: 'Flip',
    value: '翻转'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: '透视'
  },
  'filter-resize': {
    original: 'Resize',
    value: '图像大小'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: '旋转'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: '自由变换'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: '亮度/对比'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: '曲线'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: '色相/饱和度'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: '反相'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: '移轴'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: '转换至Alpha'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: '模糊'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'USM 锐化'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: '画布大小'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: '裁剪图片'
  },
  'filter-crop-left': {
    original: 'Left',
    value: '左'
  },
  'filter-crop-right': {
    original: 'Right',
    value: '右'
  },
  'filter-crop-top': {
    original: 'Top',
    value: '上'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: '下'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: '三分法'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: '填充'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: '翻转'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: '翻转图层或整个图像'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: '水平'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: '垂直'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: '翻转图像'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: '翻转图层'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: '透视'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: '变换已选图层'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: '图像大小'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: '更改图像大小'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: '旋转'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: '旋转图像'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: '图层为空'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: '变换'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: '变换已选图层。按住Shift操作。'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: '旋转'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: '翻转'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: '居中'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: '保持'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: '对齐'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: '旋转对齐/水平对齐'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: '亮度/对比'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: '更改已选图层的亮度和对比度。'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: '亮度'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: '对比'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: '曲线'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: '应用曲线到已选图层。'
  },
  'filter-curves-all': {
    original: 'All',
    value: '全部'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: '色相/饱和度'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: '更改已选图层的色相和饱和度。'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: '色相'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: '饱和度'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: '应用'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: '移轴'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: '应用移轴到已选图层。'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: '模糊半径'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: '梯度半径'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: '至Alpha'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: '对已选图层生成Alpha通道：'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: '亮度翻转'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: '亮度'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: '替换RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: '模糊'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: '应用模糊到已选图层。'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'USM锐化'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: '根据附近像素的均值应用锐化到已选图层。'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: '强度'
  },
  'filter-grid': {
    original: 'Grid',
    value: '网格'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: '给当前层添加网格线。'
  },
  'filter-noise': {
    original: 'Noise',
    value: '噪音'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: '应用噪音到已选图层。'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: '图案大小'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alpha'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: '图案'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: '为所选图层生成图案。拖动鼠标进行更多操作。'
  },
  'filter-distort': {
    original: 'Distort',
    value: '变形'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: '对所选图层进行变形操作。'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: '相位'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: '步长'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: '同步XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: '消失点'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: '消失点'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: '添加消失点至所选图层。 拖动鼠标移动消失点。'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: '线条数量'
  },
  'dropper-drop': {
    hint: 'as in, drag and drop a file',
    original: 'Drop to import',
    value: ''
  },
  'dropper-as-image': {
    original: 'As New Image',
    value: ''
  },
  'dropper-as-layer': {
    original: 'As Layer',
    value: ''
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: '打开文件...'
  },
  'import-title': {
    original: 'Import Image',
    value: '导入图像'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: '图像过大，将会进行缩小。'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: '作为图层'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: '作为图像'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: '导入图像作为新图层'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: '调整导入图像的位置。'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: '达到图层数量上限。图像将会应用到已有图层。'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: '适应'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: '拼合图像'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: '不支持此类文件类型。查看帮助。'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: '不能加载图像。文件可能受损。'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: '不支持该功能。PSD需要拼合。'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'PSD支持受限。拼合功能将会更近原始图像。'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: '图像超过{x} x {x}个像素。不能导入。'
  },
  'import-psd-size': {
    original: 'Image size',
    value: '图像大小'
  },
  'clipboard-read-fail': {
    original: 'Failed to read from clipboard.',
    value: ''
  },
  'clipboard-no-image': {
    original: 'No image found in clipboard.',
    value: ''
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: '重置'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: '适应'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: '惯性滚动'
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: '容差'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: '采样'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: '从特定图层进行色彩采样'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: '全部'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: '当前'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: '之前'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: '填充扩展'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: '扩展填充的区域（像素）'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: '连续的'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: '仅填充相连的区域'
  },
  'gradient-linear': {
    original: 'Linear',
    value: '线性'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: '对称'
  },
  'gradient-radial': {
    original: 'Radial',
    value: '径向'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: '线条'
  },
  'shape-fill': {
    original: 'Fill',
    value: '填充'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: '方形'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: '圆形'
  },
  'shape-line': {
    original: 'Line',
    value: '线条'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: '线条宽度'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: '自中心向外'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: '锁定 1:1'
  },
  'shape-auto-pan': {
    original: 'Auto-pan',
    value: ''
  },
  'shape-auto-pan-title': {
    original: 'Automatically moves as you draw',
    value: ''
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: '点击画布添加文本'
  },
  'text-title': {
    original: 'Add Text',
    value: '添加文本'
  },
  'text-text': {
    original: 'Text',
    value: '文本'
  },
  'text-font': {
    original: 'Font',
    value: '字体'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: '请输入'
  },
  'text-color': {
    original: 'Color',
    value: '色彩'
  },
  'text-size': {
    original: 'Size',
    value: '大小'
  },
  'text-line-height': {
    original: 'Line Height',
    value: '行间距'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: '字符间距'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: '左对齐'
  },
  'text-center': {
    original: 'Center',
    value: '居中'
  },
  'text-right': {
    original: 'Right',
    value: '右对齐'
  },
  'text-italic': {
    original: 'Italic',
    value: '斜体'
  },
  'text-bold': {
    original: 'Bold',
    value: '加粗'
  },
  'select-select': {
    original: 'Select',
    value: '选择'
  },
  'select-transform': {
    original: 'Transform',
    value: '变换'
  },
  'select-lasso': {
    original: 'Lasso',
    value: '套索'
  },
  'select-polygon': {
    original: 'Polygon',
    value: '多边形'
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: '替换'
  },
  'select-boolean-add': {
    original: 'Add',
    value: '添加'
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: '减去'
  },
  'select-all': {
    original: 'All',
    value: '全部'
  },
  'select-invert': {
    original: 'Invert',
    value: '翻转'
  },
  'select-reset': {
    original: 'Reset',
    value: '重置'
  },
  'select-fill': {
    original: 'Fill',
    value: '填充'
  },
  'select-erase': {
    original: 'Erase',
    value: '擦除'
  },
  'select-transform-clone': {
    original: 'Clone',
    value: '复制'
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: '已复制'
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: '移到图层'
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: '已应用变换'
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: '当前图层所选区域为空'
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: '作品未保存'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: '图画已{a}分钟{b}未保存。立刻保存以避免进度丢失。'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: '保存为PSD'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD可保留所有图层。'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: '你可以备份绘画。'
  },
  submit: {
    original: 'Submit',
    value: '提交'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: '提交作品'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: '确认提交？'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: '提交中'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: '加载app'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: '图像上传中'
  },
  unsaved: {
    original: 'Unsaved',
    value: '未保存'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: '帮助'
  },
  'tab-settings': {
    original: 'Settings',
    value: '设置'
  },
  'settings-language': {
    original: 'Language',
    value: '语言'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: '刷新后更改语言'
  },
  'settings-theme': {
    original: 'Theme',
    value: '主题'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: '保存提醒'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: '已禁用'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: '关闭保存提醒?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: '没有自动保存功能，浏览器标签页也不会永久存在。如果不定期进行保存，你的工作可能会丢失。'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: '禁用风险自负？'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: '禁用'
  },
  'theme-dark': {
    original: 'Dark',
    value: '深色'
  },
  'theme-light': {
    original: 'Light',
    value: '浅色'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: '服务条款'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: '许可'
  },
  'source-code': {
    original: 'Source Code',
    value: '源代码'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: '自动'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: '放大'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: '缩小'
  },
  radius: {
    original: 'Radius',
    value: '半径'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: '保持纵横比'
  },
  width: {
    original: 'Width',
    value: '宽度'
  },
  height: {
    original: 'Height',
    value: '高度'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: '不透明度'
  },
  scatter: {
    hint: 'for brush scatter',
    original: 'Scatter',
    value: ''
  },
  red: {
    original: 'Red',
    value: '红'
  },
  green: {
    original: 'Green',
    value: '绿'
  },
  blue: {
    original: 'Blue',
    value: '蓝'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: '橡皮擦'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: '居中'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: '图层'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: '背景'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: '缩放算法'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: '平滑'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: '像素化'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: '预览'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: '对齐'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: '以45°角度对齐'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: '锁定'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: '锁定图层透明度'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: '反向'
  },
  'compare-before': {
    original: 'Before',
    value: '之前'
  },
  'compare-after': {
    original: 'After',
    value: '之后'
  },
  loading: {
    original: 'Loading',
    value: '加载中'
  },
  more: {
    original: 'More',
    value: '更多'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x}分钟'
  },
  wip: {
    original: 'Work in progress',
    value: '此处正在施工'
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: '双击或捏合以重置浏览器缩放。'
  },
  dismiss: {
    original: 'Dismiss',
    value: '关闭'
  }
}
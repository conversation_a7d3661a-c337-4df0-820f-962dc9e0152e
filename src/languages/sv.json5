{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: '<PERSON>ränssnitt åt vänster/höger'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: 'Visa/göm gränssnittet'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: '<PERSON>ulla'
  },
  donate: {
    original: 'Donate',
    value: '<PERSON>ra'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'Huvudsida'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: 'Öppna i ny flik'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: 'Redigera'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'Arkiv'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'Pensel'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'Fyll i'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'Toning'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: 'Former'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'Text'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: 'Handverktyg'
  },
  'tool-select': {
    original: 'Select Tool',
    value: 'Områdesverktyg'
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'Zooma'
  },
  'tool-more-tools': {
    hint: 'when hovering the caret on the tool button',
    original: 'More Tools',
    value: 'Fler verktyg'
  },
  undo: {
    original: 'Undo',
    value: 'Ångra'
  },
  redo: {
    original: 'Redo',
    value: 'Gör om'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'Penna'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: 'Blandad'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'Skiss'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'Pixelerad'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'Kemi'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: 'Smeta ut'
  },
  'brush-size': {
    original: 'Size',
    value: 'Storlek'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: 'Fuktighet'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: 'Tryckkänslighet av/på'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: 'Cirkel'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'Krita'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'Kalligrafisk'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: 'Kvadrat'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'Skala'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'Gitter'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: 'Fyll'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: 'Linje'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: 'Horisontal symmetri'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: 'Vertikal symmetri'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'Toning'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: 'Genomskinlig bakgrund'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: 'Stabilisering'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: 'Linjesstabilisering'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'Pipett'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'Sekundärfärg'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: 'Välj färg manuellt'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'HTML-kod'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Till urklipp'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Ok'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'Avbryt'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: 'Stäng'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'Valt lager'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'Lager'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'kopia'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: 'Blandning'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: 'Nytt lager'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: 'Ta bort lager'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: 'Duplicera lager'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: 'Slå ihop med lagret under'
  },
  'layers-clear': {
    hint: 'to make a layer empty',
    original: 'Clear layer',
    value: 'Rensa lager'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: 'Slå ihop alla lager'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'Döp om'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'Detta lager är synligt'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'Detta lager är inte synligt'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'Visa/göm lagret'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: 'normal'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: 'mörkare'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: 'multiplicera'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: 'efterbelys färg'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: 'ljusare'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'raster'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: 'färgskugga'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'täcka över'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'mjukt ljus'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'skarpt ljus'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: 'differens'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: 'uteslutning'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: 'nyans'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: 'mättnad'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'färg'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: 'luminiscens'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'Döp om lager'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'Namn'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'Rensa namnet'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'Skiss'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: 'Färger'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: 'Skuggning'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: 'Linjer'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: 'Effekter'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: 'Förgrund'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'Slå ihop/blanda lager'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: 'Slår ihop det valda lagret med det lager som ligger under. Välj metod:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'Ej autosparning, ej molnlagring'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: 'Ny'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: 'Öppna'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: 'Spara'
  },
  'file-format': {
    original: 'File Format',
    value: 'Filformat'
  },
  'file-show-save-dialog': {
    hint: 'setting for showing/not showing a dialog where user picks folder and filename',
    original: 'Show save dialog',
    value: 'Visa sparfönster'
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Till urklipp'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'Kopiera till urklipp'
  },
  'file-paste': {
    original: 'Paste',
    value: 'Klistra in'
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'Dela'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'Webbläsarlagring'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: 'Återställs vid senare besök'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'Om webbläsarlagring'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'Något gick snett'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: 'Tomt'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: 'Lagra'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: 'Rensa'
  },
  'file-storage-clear-prompt': {
    original: 'Clear Browser Storage?',
    value: 'Rensa webbläsarlagring?'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: 'Lagrar'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: 'Skriv över'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x} min sedan'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x} timmar sedan'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x} dagar sedan'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: 'Mer än 1 mån sedan'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'Återställde från webbläsarlagringen'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'Lagrade i webbläsare'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'Webbläsarlagring misslyckades'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: 'Lagring misslyckades. Möjliga anledningar:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'Fullt lagringsminne'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'Lagring är blockerat i inkognitoflikar'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'Webbläsaren stöder inte lagring'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: 'Rensning misslyckades.'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'Ladda upp'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'Rensade lagret'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: 'Rensade det valda området'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Fyllde i'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: 'Fyllde i området'
  },
  'new-title': {
    original: 'New Image',
    value: 'Ny bild'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Befintlig'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Passa'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Enorm'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Kvadratisk'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Landskapsbild'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Porträttbild'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Bildskärm'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Video'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'A4-papper'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: 'Bildförhållande'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Ladda upp till Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'Bilden kan ses av alla som har tillgång till länken.'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: 'Titel'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: 'Ingen titel'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'Beskrivning'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'Ladda upp'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'Laddar upp...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'Uppladdning lyckades'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'Uppladdning misslyckades'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'Besök denna hemsida för att ta bort din bild från Imgur:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'Kopiera till urklipp'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: 'Beskär'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: 'Högerklicka eller tryck och håll in för att kopiera.'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'Till urklipp'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'Kopierat'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: 'Bekräfta'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: 'Dra för att beskära'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: 'Beskär/förstora'
  },
  'filter-flip': {
    original: 'Flip',
    value: 'Vänd om'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'Vinkel'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'Ändra storlek'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: 'Rotera'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: 'Omforma'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: 'Ljusst./kontrast'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'Kurvor'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: 'Nyans/mättnad'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'Invertera'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: 'Lutn./förskjutn.'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: 'Till alfa'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'Triangeloskärpa'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'Oskarp mask'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: 'Beskär / förstora'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: 'Beskär eller förläng bilden.'
  },
  'filter-crop-left': {
    original: 'Left',
    value: 'Åt vänster'
  },
  'filter-crop-right': {
    original: 'Right',
    value: 'Åt höger'
  },
  'filter-crop-top': {
    original: 'Top',
    value: 'Uppåt'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: 'Nedåt'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: 'Tredjedelsregeln'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: 'Fyll i'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: 'Vänd om'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'Vänder lagret eller till och med hela bilden.'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: 'Horisontalt'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: 'Vertikalt'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: 'Vänd om bilden'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: 'Vänd om lagret'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'Vinkel'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: 'Vinklar bilden på valt lager'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'Ändra storlek'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: 'Ändrar bildens storlek'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: 'Rotera'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: 'Roterar bilden'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'Lagret är tomt.'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: 'Omforma'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: 'Håll in Skift för ytterligare funktioner'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: 'Rotation'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: 'Vänd om'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: 'Centrera'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: 'Lås förhållande'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: 'Fäst'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: 'Fäst i rotation och position'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: 'Ljusstyrka / kontrast'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: 'Ändrar det valda lagrets ljusstyrka och kontrast.'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: 'Ljusstyrka'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'Kontrast'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'Färgkurvor'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: 'Ändra färgkurvor på valt lager.'
  },
  'filter-curves-all': {
    original: 'All',
    value: 'Alla'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: 'Nyans / mättnad'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: 'Ändra det valda lagrets nyans och mättnad.'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: 'Nyans'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: 'Mättnad'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: 'applicerat'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: 'Lutning / förskjutning'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: 'Tillämpar en lutning/förskjutnings-effekt på valt lager.'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'Oskärperadie'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'Toningsradie'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: 'Till alfakanal'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: 'Genererar en alfakanal för valt lager via:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: 'Inverterad luminiscens'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: 'Luminiscens'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Byt ut RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'Triangeloskärpa'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: 'Tillämpar triangeloskärpa på valt lager.'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'Oskarp mask'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: 'Gör bilden på lagret skarpare genom att skilja pixlar från den genomsnittliga färgen av dess grannar.'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: 'Styrka'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'Rutnät'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: 'Ritar ett rutnät på det valda lagret.'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'Visuellt brus'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: 'Lägger till brus på det valda lagret.'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'Skala'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alfa'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'Mönster'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: 'Skapar ett mönster på valt lager. Dra i förhandsvisningen för att flytta mönstret.'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'Distorsion'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: 'Förvränger det valda lagret.'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'Fas'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'Stegstorlek'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: 'Synka XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: 'Flyktpunkt'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: 'Flyktpunkt'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: 'Lägger till en flyktpunkt till det valda lagret. Dra i förhandsvisningen för att flytta runt den.'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: 'Mängd linjer'
  },
  'dropper-drop': {
    hint: 'as in, drag and drop a file',
    original: 'Drop to import',
    value: 'Släpp filen för att öppna'
  },
  'dropper-as-image': {
    original: 'As New Image',
    value: 'Som ny bild'
  },
  'dropper-as-layer': {
    original: 'As Layer',
    value: 'Som lager'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: 'Öppnar filen...'
  },
  'import-title': {
    original: 'Import Image',
    value: 'Öppna bild'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: 'För stor bild, kommer göras mindre.'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'Som lager'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: 'Som bild'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: 'Öppna bild som nytt lager'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: 'Ändra positionen på din bild.'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: 'Gränsen på lager har uppnåtts. Bilden kommer infogas på befintligt lager.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'Passa'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'Platta till bilden'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'Kleki stöder inte denna filtyp. Se Hjälpsidan för stödda filtyper.'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: 'Kunde ej öppna fil. Den må vara korrupterad.'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'Ostödda funktioner upptäcktes. PSD-filen behövde plattas till.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'Stöd för PSD-filer har vissa gränser. En tillplattad bild kommer högst sannolikt se bättre ut.'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: 'Kunde inte öppna filen. Bilden överskrider gränsen på {x} x {x} pixlar.'
  },
  'import-psd-size': {
    original: 'Image size',
    value: 'Bildstorlek'
  },
  'clipboard-read-fail': {
    original: 'Failed to read from clipboard.',
    value: 'Kunde inte läsa urklipp.'
  },
  'clipboard-no-image': {
    original: 'No image found in clipboard.',
    value: 'Kunde inte hitta någon bild i urklipp.'
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'Återställ'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'Passa'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: 'Tröghetsrullning'
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: 'Känslighet'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: 'Lyder lager'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'Vilka lager som fyllverktyget kommer ha som referens när den fyller i.'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: 'Alla'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: 'Nuvarande'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: 'Lager ovan'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: 'Väx'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: '(I pixlar) Expanderar utanför fyllningsområdet'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: 'Kräver angränsning'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: 'Fyller bara i kopplade områden'
  },
  'gradient-linear': {
    original: 'Linear',
    value: 'Linjär'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: 'Speglad linjär'
  },
  'gradient-radial': {
    original: 'Radial',
    value: 'Sfärisk'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'Linje'
  },
  'shape-fill': {
    original: 'Fill',
    value: 'Ifylld'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: 'Rektangel'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: 'Ellips'
  },
  'shape-line': {
    original: 'Line',
    value: 'Linje'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: 'Linjebredd'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: 'Inifrån ut'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: 'Låst i 1:1'
  },
  'shape-auto-pan': {
    original: 'Auto-pan',
    value: 'Panorering'
  },
  'shape-auto-pan-title': {
    original: 'Automatically moves as you draw',
    value: 'Automatisk panorering rör runt bilden medans du ritar'
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'Klicka på bilden för att lägga till en textruta'
  },
  'text-title': {
    original: 'Add Text',
    value: 'Lägg till textruta'
  },
  'text-text': {
    original: 'Text',
    value: 'Text'
  },
  'text-font': {
    original: 'Font',
    value: 'Typsnitt'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'Din text här'
  },
  'text-color': {
    original: 'Color',
    value: 'Färg'
  },
  'text-size': {
    original: 'Size',
    value: 'Storlek'
  },
  'text-line-height': {
    original: 'Line Height',
    value: 'Radavstånd'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: 'Teckenavstånd'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: 'Vänsterjustera'
  },
  'text-center': {
    original: 'Center',
    value: 'Centrera'
  },
  'text-right': {
    original: 'Right',
    value: 'Högerjustera'
  },
  'text-italic': {
    original: 'Italic',
    value: 'Kursiv'
  },
  'text-bold': {
    original: 'Bold',
    value: 'Fetstil'
  },
  'select-select': {
    original: 'Select',
    value: 'Område'
  },
  'select-transform': {
    original: 'Transform',
    value: 'Omforma'
  },
  'select-lasso': {
    original: 'Lasso',
    value: 'Lasso'
  },
  'select-polygon': {
    original: 'Polygon',
    value: 'Månghörning'
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: 'Byt ut'
  },
  'select-boolean-add': {
    original: 'Add',
    value: 'Addera'
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: 'Subtrahera'
  },
  'select-all': {
    original: 'All',
    value: 'Allting'
  },
  'select-invert': {
    original: 'Invert',
    value: 'Invertera område'
  },
  'select-reset': {
    original: 'Reset',
    value: 'Rensa'
  },
  'select-fill': {
    original: 'Fill',
    value: 'Fyll i'
  },
  'select-erase': {
    original: 'Erase',
    value: 'Sudda'
  },
  'select-transform-clone': {
    original: 'Clone',
    value: 'Klona'
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: 'Klonade område'
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: 'Flytta till lager:'
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: 'Ändringar tillämpade'
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: 'Detta lager har ingenting i det valda området.'
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: 'Osparat arbete'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: 'Bilden har inte sparats på {a} minuter{b}. För att undvika att du förlorar arbete borde du spara nu.'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'Spara som PSD-fil'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD-filer minns alla lager!'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: 'Du kan göra en backup av din bild.'
  },
  submit: {
    original: 'Submit',
    value: 'Skicka in'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: 'Skicka in bild'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: 'Vill du skicka in den här bilden?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: 'Skickar in'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Laddar'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: 'Laddar din bild'
  },
  unsaved: {
    original: 'Unsaved',
    value: 'Inte sparat'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'Hjälp'
  },
  'tab-settings': {
    original: 'Settings',
    value: 'Inställningar'
  },
  'settings-language': {
    original: 'Language',
    value: 'Språk'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'Ladda om sidan för att tillämpa ändringar.'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'Tema'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: 'Sparpåminnelse'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: 'deaktiverad'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: 'Deaktivera sparpåminnelser?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: 'Det finns ingen automatisk sparfunktion och webbläsarflikar kan lätt försvinna. Sparar du inte med jämna mellanrum så kan du förlora visst arbete.'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: 'Vill du verkligen deaktivera? (på egen risk!)'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: 'Deaktivera'
  },
  'theme-dark': {
    original: 'Dark',
    value: 'Mörkt'
  },
  'theme-light': {
    original: 'Light',
    value: 'Ljust'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Användarvillkor'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Licenser'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Källkod'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: 'automatiskt'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: 'Zooma in'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: 'Zooma ut'
  },
  radius: {
    original: 'Radius',
    value: 'Radie'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: 'Behåll bildförhållande'
  },
  width: {
    original: 'Width',
    value: 'Bredd'
  },
  height: {
    original: 'Height',
    value: 'Höjd'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: 'Synlighet'
  },
  scatter: {
    hint: 'for brush scatter',
    original: 'Scatter',
    value: 'Utspriddhet'
  },
  red: {
    original: 'Red',
    value: 'Röd'
  },
  green: {
    original: 'Green',
    value: 'Grön'
  },
  blue: {
    original: 'Blue',
    value: 'Blå'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: 'Sudd'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: 'Centrera'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'Lager'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: 'Bakgrund'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'Skalningsalgoritm'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'Utjämnad'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'Pixelerad'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'Förhandsvisning'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'Fäst'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: 'Fäst i vinkel'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: 'Lås alfa'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: 'Låser lagrets alfakanal'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: 'Invertera'
  },
  'compare-before': {
    original: 'Before',
    value: 'Före'
  },
  'compare-after': {
    original: 'After',
    value: 'Efter'
  },
  loading: {
    original: 'Loading',
    value: 'Laddar'
  },
  more: {
    original: 'More',
    value: 'Mer'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x} min'
  },
  wip: {
    original: 'Work in progress',
    value: 'Arbete pågår'
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: 'Tryck två gånger eller nyp inåt för att återställa vanlig webbläsarzoomnivå.'
  },
  dismiss: {
    original: 'Dismiss',
    value: 'Avfärda'
  }
}
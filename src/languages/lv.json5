{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: 'Pārslēgt kreiso/labo UI'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: 'Rādīt/Slēpt rīkus'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: 'Ritināt'
  },
  donate: {
    original: 'Donate',
    value: 'Ziedot'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'Sākums'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: 'Atvērt jaunā cilnē'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: 'Rediģēt'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'Fails'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'Ota'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'Aizpildīšanas rīks'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'Pārejas rīks'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: 'Figūru rīks'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'Teksta rīks'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: 'Rokas rīks'
  },
  'tool-select': {
    original: 'Select Tool',
    value: 'Atlases rīks'
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'Tālummaiņas rīks'
  },
  'tool-more-tools': {
    hint: 'when hovering the caret on the tool button',
    original: 'More Tools',
    value: 'Vairāk rīku'
  },
  undo: {
    original: 'Undo',
    value: 'Atsaukt'
  },
  redo: {
    original: 'Redo',
    value: 'Atcelt atsaukšanu'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'Pildspalva'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: 'Sajaukšana'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'Skicēts'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'Pikseļota'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'Kemija'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: 'Izsmērēšana'
  },
  'brush-size': {
    original: 'Size',
    value: 'Izmērs'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: 'Sajaukšanas pakāpe'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: 'Pārslēgt spiediena jutību'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: 'Aplis'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'Krīts'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'Kaligrāfija'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: 'Kvadrāts'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'Mērogs'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'Punktu raksts'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: 'Aizpildīt'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: 'Stroke'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: 'Horizontālā simetrija'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: 'Vertikālā simetrija'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'Krāsu pāreja'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: 'Caurspīdīgs fons'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: 'Stabilizators'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: 'Līniju stabilizators'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'Pipete'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'Sekundārā krāsa'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: 'Manuāla krāsu ievade'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'Heksadecimāls'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Kopēt'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Labi'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'Atcelt'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: 'Aizvērt'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'Aktīvais slānis'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'Slānis'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'Kopija'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: 'Sajaukšana'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: 'Jauns slānis'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: 'Noņemt slāni'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: 'Dublēt slāni'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: 'Apvienot ar apakšējo slāni'
  },
  'layers-clear': {
    hint: 'to make a layer empty',
    original: 'Clear layer',
    value: 'Notīrīt slāni'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: 'Apvienot visus'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'Pārdēvēt'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'Aktīvais slānis ir redzams'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'Aktīvais slānis ir paslēpts'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'Slāņa redzamība'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: 'Normāls'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: 'Aptumšot'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: 'Reizināt'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: 'Krāsu dedzināšana'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: 'Gaišināt'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'Ekrāns'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: 'Krāsu izvairīšanās'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'Pārklājums'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'Mīkstā gaisma'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'Cietā gaisma'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: 'Atšķirība'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: 'Izslēgšana'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: 'Tonis'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: 'Piesātinājums'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'Krāsa'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: 'Spilgtums'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'Pārdēvēt slāni'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'Nosaukums'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'Notīrīt nosaukumu'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'Skice'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: 'Krāsas'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: 'Ēnojums'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: 'Līnijas'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: 'Efekti'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: 'Priekšplāns'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'Apvienot/Sajaukt slāņus'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: 'Apvieno izvēlēto slāni ar apakšējo. Izvēlieties sajaukšanas režīmu:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'Nav automātiskās saglabāšanas'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: 'Jauns'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: 'Importēt'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: 'Saglabāt'
  },
  'file-format': {
    original: 'File Format',
    value: 'Faila formāts'
  },
  'file-show-save-dialog': {
    hint: 'setting for showing/not showing a dialog where user picks folder and filename',
    original: 'Show save dialog',
    value: 'Rādīt saglabāšanas dialogu'
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Kopēt'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'Kopēt uz starpliktuvi'
  },
  'file-paste': {
    original: 'Paste',
    value: 'Ielīmēt'
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'Kopīgot'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'Pārlūka krātuve'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: 'Atjauno'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'Par pārlūka krātuvi'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'Nevar piekļūt'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: 'Tukšs'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: 'Saglabāt'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: 'Notīrīt'
  },
  'file-storage-clear-prompt': {
    original: 'Clear Browser Storage?',
    value: 'Notīrīt pārlūka krātuvi?'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: 'Saglabā'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: 'Pārrakstīt'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: 'Pirms {x} min'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: 'Pirms {x} stundām'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: 'Pirms {x} dienām'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: 'Pirms > 1 mēneša'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'Atjaunots no pārlūka krātuves'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'Saglabāts pārlūka krātuvē'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'Neizdevās saglabāt pārlūka krātuvē'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: 'Neizdevās saglabāt. Iespējamie iemesli:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'Nav vietas diskā'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'Krātuve atspējota inkognito cilnē'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'Pārlūks neatbalsta krātuvi'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: 'Neizdevās notīrīt.'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'Augšupielādēt'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'Slānis notīrīts'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: 'Izvēlētā zona notīrīta'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Aizpildīts'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: 'Izvēle aizpildīta'
  },
  'new-title': {
    original: 'New Image',
    value: 'Jauns attēls'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Pašreizējais'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Pielāgot'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Lielizmērs'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Kvadrāts'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Ainava'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Portrets'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Ekrāns'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Video'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'DIN papīrs'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: 'Proporcija'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Augšupielādēt uz Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'Ikviens ar saiti uz jūsu augšupielādēto attēlu to varēs apskatīt.'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: 'Nosaukums'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: 'Bez nosaukuma'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'Paraksts'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'Augšupielādēt'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'Augšupielādē...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'Augšupielāde veiksmīga'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'Augšupielāde neizdevās.'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'Lai dzēstu attēlu no Imgur'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'Kopēt uz starpliktuvi'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: 'Apgriezt'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: 'Ar peles labo pogu vai turiet'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'Uz starpliktuvi'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'Kopēts.'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: 'Lietot apgriešanu'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: 'Velciet'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: 'Apgriezt/Paplašināt'
  },
  'filter-flip': {
    original: 'Flip',
    value: 'Pārvērst'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'Perspektīva'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'Mainīt izmēru'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: 'Pagriezt'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: 'Transformēt'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: 'Spilgtums/Kontrasts'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'Līknes'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: 'Tonis/Piesātinājums'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'Invertēt'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: 'Slaidu nobīde'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: 'Uz alfa'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'Trijstūra izplūšana'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'Neasuma maska'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: 'Apgriešana / Paplašināšana'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: 'Apgriež vai paplašina attēlu.'
  },
  'filter-crop-left': {
    original: 'Left',
    value: 'Kreisais'
  },
  'filter-crop-right': {
    original: 'Right',
    value: 'Labais'
  },
  'filter-crop-top': {
    original: 'Top',
    value: 'Augša'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: 'Apakša'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: 'Trešdaļu likums'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: 'Aizpildīt'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: 'Pārvēršana'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'Pārvērš slāni vai visu attēlu.'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: 'Horizontāli'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: 'Vertikāli'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: 'Pārvērst attēlu'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: 'Pārvērst slāni'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'Perspektīvas transformācija'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: 'Transformē izvēlēto slāni.'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'Izmēra maiņa'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: 'Maina attēla izmēru.'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: 'Rotācija'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: 'Pagriež attēlu.'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'Slānis ir tukšs.'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: 'Transformācija'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: 'Transformē izvēlēto slāni. Turiet Shift papildu funkcionalitātei.'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: 'Rotācija'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: 'Pārvērst'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: 'Centrēt'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: 'Saglabāt proporcijas'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: 'Piepūst'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: 'Piepūst rotāciju un pozīciju'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: 'Spilgtums / Kontrasts'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: 'Maina izvēlētā slāņa spilgtumu un kontrastu.'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: 'Spilgtums'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'Kontrasts'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'Līknes'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: 'Pielieto līknes izvēlētajam slānim.'
  },
  'filter-curves-all': {
    original: 'All',
    value: 'Visi'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: 'Tonis / Piesātinājums'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: 'Maina izvēlētā slāņa toni un piesātinājumu.'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: 'Tonis'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: 'Piesātinājums'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: 'Pielietots'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: 'Slaidu nobīdes efekts'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: 'Pielieto slaidu nobīdi izvēlētajam slānim.'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'Izplūšanas rādiuss'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'Pārejas rādiuss'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: 'Uz alfa kanālu'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: 'Ģenerē alfa kanālu izvēlētajam slānim no:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: 'Invertēts spilgtums'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: 'Spilgtums'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Aizvietot RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'Trijstūra izplūšana'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: 'Pielieto trijstūra izplūšanu izvēlētajam slānim.'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'Neasuma maska'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: 'Asina izvēlēto slāni'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: 'Stiprums'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'Režģis'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: 'Zīmē režģi uz izvēlētā slāņa.'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'Troksnis'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: 'Pievieno troksni izvēlētajam slānim.'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'Mērogs'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alfa'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'Raksts'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: 'Ģenerē rakstu uz izvēlētā slāņa. Velciet priekšskatījumu papildu kontrolei.'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'Sagrozīt'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: 'Sagroza izvēlēto slāni.'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'Fāze'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'Solļa izmērs'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: 'Sinhronizēt XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: 'Pazūdošais punkts'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: 'Pazūdošā punkta efekts'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: 'Pievieno pazūdošo punktu izvēlētajam slānim. Velciet priekšskatījumu'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: 'Līnijas'
  },
  'dropper-drop': {
    hint: 'as in, drag and drop a file',
    original: 'Drop to import',
    value: 'Nometiet'
  },
  'dropper-as-image': {
    original: 'As New Image',
    value: 'Kā jauns attēls'
  },
  'dropper-as-layer': {
    original: 'As Layer',
    value: 'Kā slānis'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: 'Atver failu...'
  },
  'import-title': {
    original: 'Import Image',
    value: 'Importēt attēlu'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: 'Attēls pārāk liels'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'Kā slānis'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: 'Kā attēls'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: 'Importēt attēlu kā jaunu slāni'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: 'Pielāgojiet importētā attēla pozīciju.'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: 'Slāņu limits sasniegts. Attēls tiks ievietots esošajā slānī.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'Pielāgot'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'Saplacināt attēlu'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'Neatbalstīts faila tips. Skatiet Palīdzību par atbalstītajiem tipiem.'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: 'Neizdevās ielādēt attēlu. Fails var būt bojāts.'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'Neatbalstītas funkcijas. PSD tika saplacināts.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'PSD atbalsts ir ierobežots. Saplacināts attēls izskatīsies precīzāk.'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: 'Attēls pārsniedz maksimālos izmērus {x} x {x} pikseļi. Nevar importēt.'
  },
  'import-psd-size': {
    original: 'Image size',
    value: 'Attēla izmērs'
  },
  'clipboard-read-fail': {
    original: 'Failed to read from clipboard.',
    value: 'Neizdevās nolasīt no starpliktuves.'
  },
  'clipboard-no-image': {
    original: 'No image found in clipboard.',
    value: 'Starpliktuvē nav attēla.'
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'Atiestatīt'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'Pielāgot'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: 'Inerces ritināšana'
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: 'Tolerance'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: 'Paraugs'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'Kurus slāņus izmantot krāsu paraugam'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: 'Visi'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: 'Aktīvais'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: 'Virspusē'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: 'Paplašināt'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: 'Paplašināt aizpildīto zonu (pikseļos)'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: 'Savienots'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: 'Aizpildīt tikai savienotās zonas'
  },
  'gradient-linear': {
    original: 'Linear',
    value: 'Lineārs'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: 'Lineārs spogulis'
  },
  'gradient-radial': {
    original: 'Radial',
    value: 'Radial'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'Stroke'
  },
  'shape-fill': {
    original: 'Fill',
    value: 'Aizpildīt'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: 'Taisnstūris'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: 'Elipse'
  },
  'shape-line': {
    original: 'Line',
    value: 'Līnija'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: 'Līnijas platums'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: 'No centra uz āru'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: 'Fiksēts 1:1'
  },
  'shape-auto-pan': {
    original: 'Auto-pan',
    value: 'Automātiskā panoramēšana'
  },
  'shape-auto-pan-title': {
    original: 'Automatically moves as you draw',
    value: 'Automātiski pārvietojas zīmējot'
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'Noklikšķiniet uz audekla'
  },
  'text-title': {
    original: 'Add Text',
    value: 'Pievienot tekstu'
  },
  'text-text': {
    original: 'Text',
    value: 'Teksts'
  },
  'text-font': {
    original: 'Font',
    value: 'Fonts'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'Jūsu teksts'
  },
  'text-color': {
    original: 'Color',
    value: 'Krāsa'
  },
  'text-size': {
    original: 'Size',
    value: 'Izmērs'
  },
  'text-line-height': {
    original: 'Line Height',
    value: 'Rindas augstums'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: 'Burtu atstarpe'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: 'Pa kreisi'
  },
  'text-center': {
    original: 'Center',
    value: 'Centrā'
  },
  'text-right': {
    original: 'Right',
    value: 'Pa labi'
  },
  'text-italic': {
    original: 'Italic',
    value: 'Slīpraksts'
  },
  'text-bold': {
    original: 'Bold',
    value: 'Treknraksts'
  },
  'select-select': {
    original: 'Select',
    value: 'Atlasīt'
  },
  'select-transform': {
    original: 'Transform',
    value: 'Transformēt'
  },
  'select-lasso': {
    original: 'Lasso',
    value: 'Lasso'
  },
  'select-polygon': {
    original: 'Polygon',
    value: 'Poligons'
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: 'Aizvietot'
  },
  'select-boolean-add': {
    original: 'Add',
    value: 'Pievienot'
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: 'Atņemt'
  },
  'select-all': {
    original: 'All',
    value: 'Visu'
  },
  'select-invert': {
    original: 'Invert',
    value: 'Invertēt'
  },
  'select-reset': {
    original: 'Reset',
    value: 'Atiestatīt'
  },
  'select-fill': {
    original: 'Fill',
    value: 'Aizpildīt'
  },
  'select-erase': {
    original: 'Erase',
    value: 'Dzēst'
  },
  'select-transform-clone': {
    original: 'Clone',
    value: 'Klonēt'
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: 'Klonēts'
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: 'Pārvietot uz slāni:'
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: 'Transformācija pielietota'
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: 'Izvēlētā zona uz aktīvā slāņa ir tukša.'
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: 'Nesaglabāts darbs'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: 'Attēls nav saglabāts {a} minūtes{b}. Saglabājiet tagad'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'Saglabāt kā PSD'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD saglabās visus slāņus.'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: 'Jūs varat dublēt savu zīmējumu.'
  },
  submit: {
    original: 'Submit',
    value: 'Iesniegt'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: 'Iesniegt zīmējumu'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: 'Iesniegt zīmējumu?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: 'Iesniedz...'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Ielādē lietotni'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: 'Gaida attēlu'
  },
  unsaved: {
    original: 'Unsaved',
    value: 'Nesaglabāts'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'Palīdzība'
  },
  'tab-settings': {
    original: 'Settings',
    value: 'Iestatījumi'
  },
  'settings-language': {
    original: 'Language',
    value: 'Valoda'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'Atjaunināsies pēc pārlādes.'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'Tēma'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: 'Saglabāšanas atgādinājums'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: 'Atspējots'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: 'Izslēgt saglabāšanas atgādinājumu?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: 'Nav automātiskās saglabāšanas'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: 'Atspējot uz savu atbildību?'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: 'Atspējot'
  },
  'theme-dark': {
    original: 'Dark',
    value: 'Tumšs'
  },
  'theme-light': {
    original: 'Light',
    value: 'Gaišs'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Lietošanas noteikumi'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Licences'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Avota kods'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: 'Automātiski'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: 'Tuvināt'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: 'Attālināt'
  },
  radius: {
    original: 'Radius',
    value: 'Rādiuss'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: 'Saglabāt proporcijas'
  },
  width: {
    original: 'Width',
    value: 'Platums'
  },
  height: {
    original: 'Height',
    value: 'Augstums'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: 'Necaurredzamība'
  },
  scatter: {
    hint: 'for brush scatter',
    original: 'Scatter',
    value: 'Izkliede'
  },
  red: {
    original: 'Red',
    value: 'Sarkans'
  },
  green: {
    original: 'Green',
    value: 'Zaļš'
  },
  blue: {
    original: 'Blue',
    value: 'Zils'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: 'Dzēšgumija'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: 'Centrēt'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'Slāņi'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: 'Fons'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'Mērogošanas algoritms'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'Gluds'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'Pikseļots'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'Priekšskatījums'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'Piepūst'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: '45° leņķa piepūšana'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: 'Bloķēt alfa'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: 'Bloķē slāņa alfa kanālu'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: 'Apgriezt'
  },
  'compare-before': {
    original: 'Before',
    value: 'Pirms'
  },
  'compare-after': {
    original: 'After',
    value: 'Pēc'
  },
  loading: {
    original: 'Loading',
    value: 'Ielādē'
  },
  more: {
    original: 'More',
    value: 'Vairāk'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x} min'
  },
  wip: {
    original: 'Work in progress',
    value: 'Darbs procesā'
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: 'Veiciet dubultskārienu vai izvelciet'
  },
  dismiss: {
    original: 'Dismiss',
    value: 'Noraidīt'
  }
}
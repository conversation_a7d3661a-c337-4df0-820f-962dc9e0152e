{"browserslist": ["> 0.2%", "last 2 versions", "not dead", "Firefox ESR", "Chrome 61", "Firefox 60", "ios_saf 9"], "scripts": {"start": "parcel serve src/index.html --no-cache", "build": "parcel build src/index.html", "build:embed": "parcel build src/embed.ts", "build:help": "parcel build src/help.html --public-url .", "lang:add": "node src/languages/generate.js add", "lang:sync": "node src/languages/generate.js sync", "lang:build": "node src/languages/generate.js build"}, "dependencies": {"@emotion/css": "^11.11.2", "@parcel/transformer-glsl": "^2.12.0", "@parcel/transformer-sass": "^2.12.0", "ag-psd": "^20.2.0", "buffer": "^6.0.3", "js-beautify": "^1.15.1", "json5": "^2.2.3", "mdn-polyfills": "^5.20.0", "parcel": "^2.12.0", "polygon-clipping": "^0.15.7", "transformation-matrix": "^2.16.1"}, "devDependencies": {"process": "^0.11.10"}}